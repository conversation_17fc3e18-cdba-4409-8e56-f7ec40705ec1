# x: 孕周 BMI 
# y: Y染色体浓度

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import statsmodels.api as sm
from pygam import GAM, s
from sklearn.metrics import r2_score
import scipy.stats as stats

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def Scatter_GA(BMI, FCY, GA):
    # 清理数据
    df = pd.DataFrame({'BMI': BMI, 'FCY': FCY, 'GA': GA})
    df_clean = df.dropna()
    
    plt.figure(figsize=(10, 6))
    ga_groups = []
    colors = ['#ED6F6E', '#9D9ECD', '#5867AF']
    for ga in df_clean['GA']:
        if ga < 15:
            ga_groups.append('a')
        elif 15 <= ga < 20:
            ga_groups.append('b')
        else:
            ga_groups.append('c')

    unique_groups = ['a', 'b', 'c']
    for i, group in enumerate(unique_groups):
        mask = [g == group for g in ga_groups]
        if any(mask):
            plt.scatter(df_clean['BMI'].values[mask], df_clean['FCY'].values[mask], c=colors[i], s=30, alpha=0.8)    
    plt.xlabel('孕妇BMI')
    plt.ylabel('Y染色体浓度')
    plt.title('孕妇BMI vs Y染色体浓度')
    plt.show()

def Scatter_BMI(GA, FCY, BMI):
    # 清理数据
    df = pd.DataFrame({'GA': GA, 'FCY': FCY, 'BMI': BMI})
    df_clean = df.dropna()
    
    plt.figure(figsize=(10, 6))
    bmi_groups = []
    colors = ['#551F33', '#CBBBC1', '#BD4146', '#E4B7BC', '#EECC68', '#F5E4C8']
    
    for i, bmi in enumerate(df_clean['BMI']):
        if bmi < 20:
            bmi_groups.append('<20')
        elif 20 <= bmi < 28:
            bmi_groups.append('[20,28)')
        elif 28 <= bmi < 32:
            bmi_groups.append('[28,32)')
        elif 32 <= bmi < 36:
            bmi_groups.append('[32,36)')
        elif 36 <= bmi < 40:
            bmi_groups.append('[36,40)')
        else:
            bmi_groups.append('≥40')

    unique_groups = ['<20', '[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']
    for i, group in enumerate(unique_groups):
        mask = [g == group for g in bmi_groups]
        if any(mask):
            plt.scatter(df_clean['GA'].values[mask], df_clean['FCY'].values[mask], c=colors[i], s=30, alpha=0.8)
    
    plt.xlabel('检测孕周')
    plt.ylabel('Y染色体浓度')
    plt.title('检测孕周 vs Y染色体浓度')
    plt.show()

def Pearson(GA, BMI, FCY, Age, GC):
    df = pd.DataFrame({'检测孕周': GA, '孕妇BMI': BMI, 'Y染色体浓度': FCY, '年龄': Age, 'GC含量': GC})
    corr = df.corr()
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(corr, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.2f')
    plt.title('Pearson Correlation Matrix')
    plt.show()

def LinearRegression(GA, BMI, FCY):
    df = pd.DataFrame({'GA': GA, 'BMI': BMI, 'FCY': FCY})
    
    df_clean = df.dropna()
    df_clean = df_clean.replace([np.inf, -np.inf], np.nan).dropna()
    
    GA_clean = df_clean['GA'].values
    BMI_clean = df_clean['BMI'].values
    FCY_clean = df_clean['FCY'].values
    
    X = np.column_stack([GA_clean, BMI_clean])
    X = sm.add_constant(X)
    y = FCY_clean
    
    model = sm.OLS(y, X).fit()
    y_pred = model.predict(X)
    
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
    
    # GA vs FCY
    ax1.scatter(GA_clean, FCY_clean, alpha=0.6, color='#F6C63C', s=20)
    z1 = np.polyfit(GA_clean, FCY_clean, 1)
    p1 = np.poly1d(z1)
    ax1.plot(GA_clean, p1(GA_clean), alpha=0.8, color='#EFA143')
    ax1.set_xlabel('检测孕周')
    ax1.set_ylabel('Y染色体浓度')
    ax1.set_title('检测孕周 vs Y染色体浓度')
    ax1.grid(True, alpha=0.3)
    
    # BMI vs FCY  
    ax2.scatter(BMI_clean, FCY_clean, alpha=0.6, color='#D96558', s=20)
    z2 = np.polyfit(BMI_clean, FCY_clean, 1)
    p2 = np.poly1d(z2)
    ax2.plot(BMI_clean, p2(BMI_clean), alpha=0.8, color='#B43970')
    ax2.set_xlabel('孕妇BMI')
    ax2.set_ylabel('Y染色体浓度')
    ax2.set_title('孕妇BMI vs Y染色体浓度')
    ax2.grid(True, alpha=0.3)
    
    # Actual vs Predict
    ax3.scatter(FCY_clean, y_pred, alpha=0.6, color='#692F7C', s=20)
    ax3.plot([FCY_clean.min(), FCY_clean.max()], [FCY_clean.min(), FCY_clean.max()], alpha=0.8, color='#282A62')
    ax3.set_xlabel('真实Y染色体浓度')
    ax3.set_ylabel('预测Y染色体浓度')
    ax3.set_title('真实Y染色体浓度 vs 预测Y染色体浓度')
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()
    
    # 输出模型摘要
    print(f"R²: {model.rsquared:.4f}")
    print(f"Adjusted R²: {model.rsquared_adj:.4f}")
    
    return model

def MultipleRegression(GA, FCY, BMI):
    GA2 = GA**2
    GABMI = GA * BMI
    BMI2 = BMI**2
    df = pd.DataFrame({'GA': GA, 'GA2': GA2, 'GABMI': GABMI, 'BMI': BMI, 'BMI2': BMI2, 'FCY': FCY})

    df_clean = df.dropna()
    df_clean = df_clean.replace([np.inf, -np.inf], np.nan).dropna()

    X = df_clean[['GA', 'GA2', 'GABMI', 'BMI', 'BMI2']]
    X = sm.add_constant(X)    
    y = df_clean["FCY"]

    model = sm.OLS(y, X).fit()
    print(model.summary())
    print("R²: ", model.rsquared)
    print("Adjusted R²: ", model.rsquared_adj)

    residuals = model.resid
    plt.figure(figsize=(10, 6))
    plt.scatter(df_clean["GA"], residuals, alpha=0.6, s=20)
    plt.xlabel('GA')
    plt.ylabel('Residuals')
    plt.show()
    
    predicted_y = model.predict(X)
    plt.figure(figsize=(12, 5))
    plt.scatter(df_clean["GA"], df_clean["FCY"], label="Actual", alpha=0.7, s=20)
    plt.scatter(df_clean["GA"], predicted_y, label="Predicted", alpha=0.7, s=20)
    plt.xlabel("GA")
    plt.ylabel("FCY")
    plt.legend()
    plt.title("Actual vs Predicted Values")
    plt.grid(True, alpha=0.3)    
    plt.tight_layout()
    plt.show()

def MultipleRegressionCubic(GA, FCY, BMI):
    # 三次项
    GA2 = GA**2
    GA3 = GA**3
    BMI2 = BMI**2
    BMI3 = BMI**3
    
    # 二次交互项
    GABMI = GA * BMI
    GA2BMI = GA**2 * BMI
    GABMI2 = GA * BMI**2
    
    df = pd.DataFrame({
        'GA': GA, 'GA2': GA2, 'GA3': GA3,
        'BMI': BMI, 'BMI2': BMI2, 'BMI3': BMI3,
        'GABMI': GABMI, 'GA2BMI': GA2BMI, 'GABMI2': GABMI2,
        'FCY': FCY
    })

    df_clean = df.dropna()
    df_clean = df_clean.replace([np.inf, -np.inf], np.nan).dropna()

    X = df_clean[['GA', 'GA2', 'GA3', 'BMI', 'BMI2', 'BMI3', 'GABMI', 'GA2BMI', 'GABMI2']]
    X = sm.add_constant(X)    
    y = df_clean["FCY"]

    model = sm.OLS(y, X).fit()
    print(model.summary())
    print("R²: ", model.rsquared)
    print("调整R²: ", model.rsquared_adj)

    residuals = model.resid
    plt.figure(figsize=(10, 6))
    plt.scatter(df_clean["GA"], residuals, alpha=0.6, s=20, color='#ADDB88')
    plt.xlabel('检测孕周')
    plt.ylabel('残差')
    plt.title('残差 vs 检测孕周')
    plt.grid(True, alpha=0.3)
    plt.show()
    
    predicted_y = model.predict(X)
    plt.figure(figsize=(12, 5))
    plt.scatter(df_clean["GA"], df_clean["FCY"], label="Actual", alpha=0.7, s=20)
    plt.scatter(df_clean["GA"], predicted_y, label="Predicted", alpha=0.7, s=20)
    plt.xlabel("检测孕周")
    plt.ylabel("Y染色体浓度")
    plt.legend()
    plt.title("真实Y染色体浓度 vs 预测Y染色体浓度")
    plt.grid(True, alpha=0.3)    
    plt.tight_layout()
    plt.show()

    return model

def Gam_model(GA, BMI, FCY):
    df = pd.DataFrame({'GA': GA, 'BMI': BMI, 'FCY': FCY}).dropna()
    X, y = df[['GA', 'BMI']].values, df['FCY'].values

    # GAM 建模
    gam = GAM(s(0, n_splines=25) + s(1, n_splines=25))
    gam.fit(X, y)
    y_pred = gam.predict(X)

    # 评估
    r2 = r2_score(y, y_pred)
    n = len(y)    # 样本数量
    p = 2         # 预测变量数量 (GA, BMI)
    adj_r2 = 1 - (1 - r2) * (n - 1) / (n - p - 1)
    
    print("GAM R²:", r2)
    print("Adjusted R²:", adj_r2)
    
    # ANOVA 检验
    print("\nANOVA 检验：")
    ss_total = np.sum((y - np.mean(y))**2)
    ss_reg = np.sum((y_pred - np.mean(y))**2)
    ss_res = np.sum((y - y_pred)**2)
    
    df_reg = p
    df_res = n - p - 1
    df_total = n - 1
    
    ms_reg = ss_reg / df_reg
    ms_res = ss_res / df_res
    
    F_stat = ms_reg / ms_res
    p_value = 1 - stats.f.cdf(F_stat, df_reg, df_res)
    
    print(f"  F统计量: {F_stat:.4f}")
    print(f"  P值: {p_value:.4f}")
    print(f"  显著性: {'显著' if p_value < 0.05 else '不显著'}")

    # 实际 vs 预测
    plt.figure(figsize=(6, 5))
    plt.scatter(y, y_pred, alpha=0.6, color='teal')
    plt.plot([y.min(), y.max()], [y.min(), y.max()], color='#9ED17B')
    plt.xlabel('真实Y染色体浓度')
    plt.ylabel('预测Y染色体浓度')
    plt.title('真实Y染色体浓度 vs 预测Y染色体浓度')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # 残差分析图
    residuals = y - y_pred
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 残差 vs 预测值
    axes[0, 0].scatter(y_pred, residuals, alpha=0.6, color='#FABB6E')
    axes[0, 0].axhline(y=0, color='#FC8002')
    axes[0, 0].set_xlabel('预测Y染色体浓度')
    axes[0, 0].set_ylabel('残差')
    axes[0, 0].set_title('残差 vs 预测Y染色体浓度')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 残差 vs GA
    axes[0, 1].scatter(df['GA'].values, residuals, alpha=0.6, color='#92C2DD')
    axes[0, 1].axhline(y=0, color='#4995C6')
    axes[0, 1].set_xlabel('检测孕周')
    axes[0, 1].set_ylabel('残差')
    axes[0, 1].set_title('残差 vs 检测孕周')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 残差 vs BMI
    axes[1, 0].scatter(df['BMI'].values, residuals, alpha=0.6, color='#B4B4D5')
    axes[1, 0].axhline(y=0, color='#8481BA')
    axes[1, 0].set_xlabel('孕妇BMI')
    axes[1, 0].set_ylabel('残差')
    axes[1, 0].set_title('残差 vs 孕妇BMI')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 残差正态性检验 (QQ图)
    stats.probplot(residuals, dist="norm", plot=axes[1, 1], color='#EE4431')
    axes[1, 1].set_title('Normal Q-Q Plot of Residuals', color='#B9181A')
    
    plt.tight_layout()
    plt.show()

    # GA vs FCY 散点 + 平滑曲线
    plt.figure(figsize=(6, 5))
    sns.regplot(x='GA', y='FCY', data=df, scatter_kws={'alpha': 0.6, 'color': 'purple'},
                line_kws={'color': 'red', 'label': 'LOESS'})
    plt.title('GA vs FCY')
    plt.xlabel('Gestational Age (weeks)')
    plt.ylabel('FCY')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # BMI vs FCY 散点 + 平滑曲线
    plt.figure(figsize=(6, 5))
    sns.regplot(x='BMI', y='FCY', data=df, scatter_kws={'alpha': 0.6, 'color': 'orange'},
                line_kws={'color': 'red', 'label': 'LOESS'})
    plt.title('BMI vs FCY')
    plt.xlabel('BMI')
    plt.ylabel('FCY')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

if __name__ == '__main__':
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    # 检测孕周 & 孕妇BMI & Y染色体浓度
    GA = df['检测孕周']
    BMI = df['孕妇BMI']
    FCY = df['Y染色体浓度']
    Age = df['年龄']
    GC = df['GC含量']
    # Scatter_GA(BMI, FCY, GA)
    # Scatter_BMI(GA, FCY, BMI)
    # Pearson(GA, BMI, FCY, Age, GC)
    #LinearRegression(GA, BMI, FCY)
    # MultipleRegression(GA, FCY, BMI)
    # MultipleRegressionCubic(GA, FCY, BMI)
    Gam_model(GA, BMI, FCY)
    